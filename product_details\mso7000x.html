<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSO7000X系列高带宽混合信号示波器 - 广州迅屿科技</title>
    <meta name="description" content="优利德MSO7000X系列高带宽混合信号示波器，2.5GHz带宽，10GSa/s采样，1Gpts存储，丰富协议分析与智能化平台，适用于高速信号分析。">
    <meta name="keywords" content="MSO7000X, 混合信号示波器, 优利德, 2.5GHz, 10GSa/s, 1Gpts, Win10, 眼图, 抖动分析, 协议分析">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 详情页样式同utl8500x.html */
        .product-detail { padding: 40px 0; }
        .product-header { margin-bottom: 30px; text-align: center; }
        .product-header h1 { font-size: 2.2rem; color: #333; margin-bottom: 10px; }
        .product-overview { display: flex; gap: 30px; margin-bottom: 40px; }
        .product-image-main { flex: 0 0 50%; max-width: 50%; }
        .product-image-main img { width: 100%; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .product-thumbnails { display: flex; margin-top: 15px; gap: 10px; }
        .product-thumbnail { width: 80px; height: 80px; border-radius: 6px; overflow: hidden; cursor: pointer; border: 2px solid #ddd; transition: border-color 0.3s; }
        .product-thumbnail:hover { border-color: #007bff; }
        .product-thumbnail img { width: 100%; height: 100%; object-fit: cover; }
        .product-intro { flex: 1; }
        .product-intro h2 { font-size: 1.8rem; color: #333; margin-bottom: 20px; position: relative; padding-bottom: 15px; }
        .product-intro h2:after { content: ''; position: absolute; bottom: 0; left: 0; width: 60px; height: 3px; background: linear-gradient(to right, #007bff, #00a6ff); }
        .product-intro p { margin-bottom: 15px; color: #555; line-height: 1.6; }
        .feature-list-columns { display: flex; flex-wrap: wrap; margin: 0 -10px; list-style-type: none; padding: 0; margin-top: 20px; }
        .feature-list-columns li { width: calc(50% - 20px); margin: 0 10px; box-sizing: border-box; position: relative; padding: 8px 0 8px 30px; color: #555; }
        .feature-list-columns li:before { content: '\f00c'; font-family: 'Font Awesome 5 Free'; font-weight: 900; color: #007bff; position: absolute; left: 0; top: 8px; }
        .pdf-content { margin: 40px 0; }
        .pdf-viewer { width: 100%; height: 800px; margin-bottom: 20px; border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius: 8px; }
        .contact-info { margin: 30px 0; padding: 25px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; box-shadow: 0 3px 10px rgba(0,0,0,0.05); }
        .contact-info h3 { font-size: 1.4rem; color: #333; margin-bottom: 15px; }
        .contact-info p { color: #555; margin-bottom: 10px; line-height: 1.6; }
        @media (max-width: 992px) { .product-overview { flex-direction: column; } .product-image-main { max-width: 100%; } .pdf-viewer { height: 600px; } }
        @media (max-width: 768px) { .product-header h1 { font-size: 1.8rem; } .product-intro h2 { font-size: 1.5rem; } .pdf-viewer { height: 500px; } .feature-list-columns li { width: 100%; } }
        @media (max-width: 480px) { .product-thumbnail { width: 60px; height: 60px; } .pdf-viewer { height: 400px; } }
    </style>
</head>
<body>
    <!-- 头部区域同utl8500x.html -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img src="../LOGO_全.png" alt="广州迅屿科技">
                        <div class="company-slogan">专业仪器仪表销售与维护服务商</div>
                    </div>
                    <div class="search-box">
                        <input type="text" placeholder="搜索产品...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <div class="hotline">
                        <span class="hotline-label">服务热线</span>
                        <span class="hotline-number">020-31801362</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="header-nav">
            <div class="container">
                <nav>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li class="nav-item">
                            <a href="../products.html">产品中心</a>
                            <div class="dropdown-menu">
                                <div class="menu-title">产品分类</div>
                                <div class="dropdown-columns">
                                    <div class="dropdown-column">
                                        <a href="../products.html#oscilloscopes"><i class="fas fa-wave-square"></i> 示波器</a>
                                        <a href="../products.html#power-loads"><i class="fas fa-bolt"></i> 功率负载</a>
                                        <a href="../products.html#current-probes"><i class="fas fa-plug"></i> 电流探头</a>
                                        <a href="../products.html#voltage-probes"><i class="fas fa-bolt"></i> 电压探头</a>
                                        <a href="../products.html#data-acquisition"><i class="fas fa-database"></i> 数据采集卡</a>
                                    </div>
                                    <div class="dropdown-column">
                                        <a href="../products.html#multimeters"><i class="fas fa-tachometer-alt"></i> 万用表/毫伏表</a>
                                        <a href="../products.html#power-supply"><i class="fas fa-plug"></i> 直流供电</a>
                                        <a href="../products.html#signal-generators"><i class="fas fa-signal"></i> 信号发生器</a>
                                        <a href="../products.html#analyzers"><i class="fas fa-chart-line"></i> 射频微波类仪器</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="../services.html">服务支持</a>
                            <div class="dropdown-menu">
                                <a href="../services.html#maintenance"><i class="fas fa-tools"></i> 维修服务</a>
                                <a href="../services.html#calibration"><i class="fas fa-balance-scale"></i> 校准服务</a>
                                <a href="../services.html#custom"><i class="fas fa-cogs"></i> 定制服务</a>
                                <a href="../services.html#training"><i class="fas fa-chalkboard-teacher"></i> 技术培训</a>
                            </div>
                        </li>
                        <li><a href="../about.html">关于我们</a></li>
                        <li><a href="../contact.html">联系我们</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <div class="container">
            <div class="breadcrumb-nav">
                <a href="../index.html"><i class="fas fa-home"></i> 首页</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html">产品中心</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html#oscilloscopes-mixed-signal">混合信号示波器</a>
                <i class="fas fa-angle-right"></i>
                <span>MSO7000X系列高带宽混合信号示波器</span>
            </div>
        </div>
    </div>
    <!-- 产品详情内容 -->
    <main>
        <section class="product-detail">
            <div class="container">
                <div class="product-header">
                    <h1>MSO7000X系列高带宽混合信号示波器</h1>
                </div>
                <div class="product-overview">
                    <div class="product-image-main">
                        <img src="../优利德/混合信号示波器/MSO7000X_1.png" alt="MSO7000X系列高带宽混合信号示波器" id="main-image">
                        <div class="product-thumbnails">
                            <div class="product-thumbnail" onclick="changeImage('../优利德/混合信号示波器/MSO7000X_1.png')">
                                <img src="../优利德/混合信号示波器/MSO7000X_1.png" alt="MSO7000X正面图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/混合信号示波器/MSO7000X_2.png')">
                                <img src="../优利德/混合信号示波器/MSO7000X_2.png" alt="MSO7000X功能图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/混合信号示波器/MSO7000X_3.png')">
                                <img src="../优利德/混合信号示波器/MSO7000X_3.png" alt="MSO7000X特性图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/混合信号示波器/MSO7000X_4.png')">
                                <img src="../优利德/混合信号示波器/MSO7000X_4.png" alt="MSO7000X应用图">
                            </div>
                        </div>
                    </div>
                    <div class="product-intro">
                        <h2>产品概述</h2>
                        <p>MSO7000X系列高带宽混合信号示波器，拥有非凡的性能指标，全面细致的分析功能，人性化的触控体验，助您在科技的海洋无限探索。2GHz带宽是高端示波器的起点，MSO7000X系列带宽高达2.5GHz，在高速信号分析中有明显的优势。MSO7000X波形捕获率高达2,000,000 wfms/s，结合1Gpts超长存储深度，显著提升异常信号捕获能力，以及波形细节测量和分析能力。搭载Win10 64位操作系统，为用户提供稳定可扩展的系统平台，通过提供示波器API接口，显著提升系统的智能化程度，满足日益复杂的用户需求。</p>
                        <h2>产品特点</h2>
                        <ul class="feature-list-columns">
                            <li>模拟通道带宽：1GHz/2GHz/2.5GHz</li>
                            <li>实时采样率：10GSa/s</li>
                            <li>最高存储深度：1Gpts</li>
                            <li>波形捕获率：≥2,000,000 wfms/s（顺序模式）</li>
                            <li>多种数学运算，内置增强FFT分析和峰值搜索功能</li>
                            <li>支持超过48种自动参数测量、参数快照</li>
                            <li>高效分析工具：区域直方图、趋势图和追踪图分析</li>
                            <li>MATLAB嵌入式编程，Windows+发现更多可能</li>
                            <li>多达11种串行协议分析</li>
                            <li>支持WebServer-兼容多平台远程操控</li>
                            <li>内置60MHz函数波形任意发生器（选件）*</li>
                            <li>实时眼图和抖动分析（选件）*</li>
                            <li>内置高级的电源分析软件（选件）*</li>
                            <li>15.6FHD高清屏+手势触控</li>
                            <li>简约直观的面板设计，快捷的操作控制</li>
                            <li>丰富的接口：USB Host & Device、LAN、HDMI、AUX In/Out、10MHz Ref In/Out</li>
                        </ul>
                    </div>
                </div>
                <!-- PDF产品手册 -->
                <div class="pdf-content">
                    <h2>产品手册</h2>
                    <iframe class="pdf-viewer" src="../优利德/混合信号示波器/MSO7000X系列 高带宽混合信号示波器.pdf"></iframe>
                </div>
                <!-- 联系信息 -->
                <div class="contact-info">
                    <h3>需要更多产品信息？</h3>
                    <p>如果您对MSO7000X系列高带宽混合信号示波器有任何疑问或需求，欢迎联系我们的产品专家。</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675 / 020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
            </div>
        </section>
    </main>
    <!-- 页脚同utl8500x.html -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <p><i class="fas fa-map-marker-alt"></i> 地址：广州市天河区黄村三联路20号尚北科创园C栋405</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675</p>
                    <p><i class="fas fa-phone"></i> 固定电话：020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../products.html">产品中心</a></li>
                        <li><a href="../services.html">服务支持</a></li>
                        <li><a href="../about.html">关于我们</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 广州迅屿科技有限公司 版权所有 | 粤ICP备2025434012号</p>
            </div>
        </div>
    </footer>
    <script>
        function changeImage(src) {
            document.getElementById('main-image').src = src;
        }
    </script>
</body>
</html> 