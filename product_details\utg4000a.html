<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UTG4000A系列函数/任意波形发生器 - 广州迅屿科技</title>
    <meta name="description" content="优利德UTG4000A系列函数/任意波形发生器，集多功能于一体，提供高精度信号输出，最高实时采样率500MS/s，适用于科研和工业测试领域。">
    <meta name="keywords" content="UTG4000A, 函数发生器, 任意波形发生器, 优利德, 高精度, 多功能, 信号发生器">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 产品详情页特定样式 */
        .product-detail {
            padding: 40px 0;
        }
        
        .product-header {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .product-header h1 {
            font-size: 2.2rem;
            color: #333;
            margin-bottom: 10px;
        }
        
        .product-overview {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .product-image-main {
            flex: 0 0 50%;
            max-width: 50%;
        }
        
        .product-image-main img {
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .product-thumbnails {
            display: flex;
            margin-top: 15px;
            gap: 10px;
        }
        
        .product-thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid #ddd;
            transition: border-color 0.3s;
        }
        
        .product-thumbnail:hover {
            border-color: #007bff;
        }
        
        .product-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .product-intro {
            flex: 1;
        }
        
        .product-intro h2 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 15px;
        }
        
        .product-intro h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, #007bff, #00a6ff);
        }
        
        .product-intro p {
            margin-bottom: 15px;
            color: #555;
            line-height: 1.6;
        }
        
        .feature-list-columns {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
            list-style-type: none;
            padding: 0;
            margin-top: 20px;
        }
        
        .feature-list-columns li {
            width: calc(50% - 20px);
            margin: 0 10px;
            box-sizing: border-box;
            position: relative;
            padding: 8px 0 8px 30px;
            color: #555;
        }
        
        .feature-list-columns li:before {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            color: #007bff;
            position: absolute;
            left: 0;
            top: 8px;
        }
        
        .pdf-content {
            margin: 40px 0;
        }
        
        .pdf-viewer {
            width: 100%;
            height: 800px;
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        
        .contact-info {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .contact-info h3 {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .contact-info p {
            color: #555;
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        @media (max-width: 992px) {
            .product-overview {
                flex-direction: column;
            }
            
            .product-image-main {
                max-width: 100%;
            }
            
            .pdf-viewer {
                height: 600px;
            }
        }
        
        @media (max-width: 768px) {
            .product-header h1 {
                font-size: 1.8rem;
            }
            
            .product-intro h2 {
                font-size: 1.5rem;
            }
            
            .pdf-viewer {
                height: 500px;
            }
            
            .feature-list-columns li {
                width: 100%;
            }
        }
        
        @media (max-width: 480px) {
            .product-thumbnail {
                width: 60px;
                height: 60px;
            }
            
            .pdf-viewer {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img src="../LOGO_全.png" alt="广州迅屿科技">
                        <div class="company-slogan">专业仪器仪表销售与维护服务商</div>
                    </div>
                    <div class="search-box">
                        <input type="text" placeholder="搜索产品...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <div class="hotline">
                        <span class="hotline-label">服务热线</span>
                        <span class="hotline-number">020-31801362</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="header-nav">
            <div class="container">
                <nav>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li class="nav-item">
                            <a href="../products.html">产品中心</a>
                            <div class="dropdown-menu">
                                <div class="menu-title">产品分类</div>
                                <a href="../products.html#power-loads"><i class="fas fa-bolt"></i> 功率负载</a>
                                <a href="../products.html#oscilloscopes"><i class="fas fa-wave-square"></i> 示波器</a>
                                <a href="../products.html#current-probes"><i class="fas fa-plug"></i> 电流探头</a>
                                <a href="../products.html#voltage-probes"><i class="fas fa-bolt"></i> 电压探头</a>
                                <a href="../products.html#data-acquisition"><i class="fas fa-database"></i> 数据采集卡</a>
                                <a href="../products.html#analyzers-spectrum"><i class="fas fa-chart-line"></i> 频谱分析仪</a>
                                <a href="../products.html#signal-generators-rf"><i class="fas fa-broadcast-tower"></i> 射频信号发生器</a>
                                <a href="../products.html#signal-generators-function"><i class="fas fa-signal"></i> 函数/任意波形发生器</a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="../services.html">服务支持</a>
                            <div class="dropdown-menu">
                                <a href="../services.html#maintenance"><i class="fas fa-tools"></i> 维修服务</a>
                                <a href="../services.html#calibration"><i class="fas fa-balance-scale"></i> 校准服务</a>
                                <a href="../services.html#custom"><i class="fas fa-cogs"></i> 定制服务</a>
                                <a href="../services.html#training"><i class="fas fa-chalkboard-teacher"></i> 技术培训</a>
                            </div>
                        </li>
                        <li><a href="../about.html">关于我们</a></li>
                        <li><a href="../contact.html">联系我们</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <div class="container">
            <div class="breadcrumb-nav">
                <a href="../index.html"><i class="fas fa-home"></i> 首页</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html">产品中心</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html#signal-generators-function">函数/任意波形发生器</a>
                <i class="fas fa-angle-right"></i>
                <span>UTG4000A系列函数/任意波形发生器</span>
            </div>
        </div>
    </div>

    <!-- 产品详情内容 -->
    <main>
        <section class="product-detail">
            <div class="container">
                <div class="product-header">
                    <h1>UTG4000A系列函数/任意波形发生器</h1>
                </div>
                
                <div class="product-overview">
                    <div class="product-image-main">
                        <img src="../优利德/函数任意波形发生器/UTG4000A1.png" alt="UTG4000A系列函数/任意波形发生器" id="main-image">
                        <div class="product-thumbnails">
                            <div class="product-thumbnail" onclick="changeImage('../优利德/函数任意波形发生器/UTG4000A1.png')">
                                <img src="../优利德/函数任意波形发生器/UTG4000A1.png" alt="UTG4000A系列正面图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/函数任意波形发生器/UTG4000A2.png')">
                                <img src="../优利德/函数任意波形发生器/UTG4000A2.png" alt="UTG4000A系列功能图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/函数任意波形发生器/UTG4000A3.png')">
                                <img src="../优利德/函数任意波形发生器/UTG4000A3.png" alt="UTG4000A系列界面图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/函数任意波形发生器/UTG4000A4.png')">
                                <img src="../优利德/函数任意波形发生器/UTG4000A4.png" alt="UTG4000A系列应用图">
                            </div>
                        </div>
                    </div>
                    <div class="product-intro">
                        <h2>产品概述</h2>
                        <p>UTG4000A系列集函数发生器、任意波形发生器、脉冲发生器、谐波发生器、模拟/数字调制器、频率计等功能于一体的多功能混合信号发生器。具有1μHz频率分辨率以及1mV的幅度分辨率，可生成精确、稳定、纯净、低失真的输出信号，还能提供高频率且具有上升沿和下降沿1ns分辨率的数字合成脉冲波。</p>
                        
                        <p>产品采用独立等性能双通道设计，最高实时采样率高达500MS/s，最大任意波长度高达32Mpts，同时具备7GB非易失波形存储。国内首款选配数字信号发生器输出模块，配备超宽8英寸LCD高清屏显示，人性化的界面设计及合理的功能键盘布局，给用户带来更为人性化的使用体验。</p>
                        
                        <h2>产品特点</h2>
                        <ul class="feature-list-columns">
                            <li>500MSa/s采样速度和16bit垂直分辨率</li>
                            <li>32Mpts任意波存储器，7GB非易失波形存储</li>
                            <li>丰富的调制类型：AM、FM、PM、ASK、FSK等</li>
                            <li>支持BPSK、QPSK、OSK、PWM、SUM、QAM</li>
                            <li>80MHz/160MHz/200MHz 正弦波输出</li>
                            <li>标配等性能双通道</li>
                            <li>标配16次谐波发生器</li>
                            <li>支持数字任意波输出接口</li>
                            <li>超宽8英寸LCD高清屏显示</li>
                            <li>人性化的界面设计及功能布局</li>
                            <li>高精度1μHz频率分辨率</li>
                            <li>高精度1mV幅度分辨率</li>
                        </ul>
                    </div>
                </div>
                
                <!-- PDF产品手册 -->
                <div class="pdf-content">
                    <h2>产品手册</h2>
                    <iframe class="pdf-viewer" src="../优利德/函数任意波形发生器/UTG4000A.pdf"></iframe>
                </div>
                
                <!-- 联系信息 -->
                <div class="contact-info">
                    <h3>需要更多产品信息？</h3>
                    <p>如果您对UTG4000A系列函数/任意波形发生器有任何疑问或需求，欢迎联系我们的产品专家。</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675 / 020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <p><i class="fas fa-map-marker-alt"></i> 地址：广州市天河区黄村三联路20号尚北科创园C栋405</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675</p>
                    <p><i class="fas fa-phone"></i> 固定电话：020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../products.html">产品中心</a></li>
                        <li><a href="../services.html">服务支持</a></li>
                        <li><a href="../about.html">关于我们</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 广州迅屿科技有限公司 版权所有 | 粤ICP备2025434012号</p>
            </div>
        </div>
    </footer>

    <script>
        function changeImage(src) {
            document.getElementById('main-image').src = src;
        }
    </script>
</body>
</html> 