<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSO3000HD系列高分辨率示波器 - 广州迅屿科技</title>
    <meta name="description" content="优利德MSO3000HD系列高分辨率示波器，集成4信号通道、16通道逻辑分析仪和2通道任意波形发生器，12bit ADC分辨率，最大采样率2.5GSa/s，存储深度500Mpts，适用于高质量波形分析。">
    <meta name="keywords" content="MSO3000HD, 高分辨率示波器, 优利德, 12bit, 2.5GSa/s, 500Mpts, 逻辑分析仪, 任意波形发生器">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 详情页样式同utl8500x.html */
        .product-detail { padding: 40px 0; }
        .product-header { margin-bottom: 30px; text-align: center; }
        .product-header h1 { font-size: 2.2rem; color: #333; margin-bottom: 10px; }
        .product-overview { display: flex; gap: 30px; margin-bottom: 40px; }
        .product-image-main { flex: 0 0 50%; max-width: 50%; }
        .product-image-main img { width: 100%; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .product-thumbnails { display: flex; margin-top: 15px; gap: 10px; }
        .product-thumbnail { width: 80px; height: 80px; border-radius: 6px; overflow: hidden; cursor: pointer; border: 2px solid #ddd; transition: border-color 0.3s; }
        .product-thumbnail:hover { border-color: #007bff; }
        .product-thumbnail img { width: 100%; height: 100%; object-fit: cover; }
        .product-intro { flex: 1; }
        .product-intro h2 { font-size: 1.8rem; color: #333; margin-bottom: 20px; position: relative; padding-bottom: 15px; }
        .product-intro h2:after { content: ''; position: absolute; bottom: 0; left: 0; width: 60px; height: 3px; background: linear-gradient(to right, #007bff, #00a6ff); }
        .product-intro p { margin-bottom: 15px; color: #555; line-height: 1.6; }
        .feature-list-columns { display: flex; flex-wrap: wrap; margin: 0 -10px; list-style-type: none; padding: 0; margin-top: 20px; }
        .feature-list-columns li { width: calc(50% - 20px); margin: 0 10px; box-sizing: border-box; position: relative; padding: 8px 0 8px 30px; color: #555; }
        .feature-list-columns li:before { content: '\f00c'; font-family: 'Font Awesome 5 Free'; font-weight: 900; color: #007bff; position: absolute; left: 0; top: 8px; }
        .pdf-content { margin: 40px 0; }
        .pdf-viewer { width: 100%; height: 800px; margin-bottom: 20px; border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius: 8px; }
        .contact-info { margin: 30px 0; padding: 25px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; box-shadow: 0 3px 10px rgba(0,0,0,0.05); }
        .contact-info h3 { font-size: 1.4rem; color: #333; margin-bottom: 15px; }
        .contact-info p { color: #555; margin-bottom: 10px; line-height: 1.6; }
        @media (max-width: 992px) { .product-overview { flex-direction: column; } .product-image-main { max-width: 100%; } .pdf-viewer { height: 600px; } }
        @media (max-width: 768px) { .product-header h1 { font-size: 1.8rem; } .product-intro h2 { font-size: 1.5rem; } .pdf-viewer { height: 500px; } .feature-list-columns li { width: 100%; } }
        @media (max-width: 480px) { .product-thumbnail { width: 60px; height: 60px; } .pdf-viewer { height: 400px; } }
    </style>
</head>
<body>
    <!-- 头部区域同utl8500x.html -->
    <header>
        <div class="header-top">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img src="../LOGO_全.png" alt="广州迅屿科技">
                        <div class="company-slogan">专业仪器仪表销售与维护服务商</div>
                    </div>
                    <div class="search-box">
                        <input type="text" placeholder="搜索产品...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                    <div class="hotline">
                        <span class="hotline-label">服务热线</span>
                        <span class="hotline-number">020-31801362</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="header-nav">
            <div class="container">
                <nav>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li class="nav-item">
                            <a href="../products.html">产品中心</a>
                            <div class="dropdown-menu">
                                <div class="menu-title">产品分类</div>
                                <div class="dropdown-columns">
                                    <div class="dropdown-column">
                                        <a href="../products.html#oscilloscopes"><i class="fas fa-wave-square"></i> 示波器</a>
                                        <a href="../products.html#power-loads"><i class="fas fa-bolt"></i> 功率负载</a>
                                        <a href="../products.html#current-probes"><i class="fas fa-plug"></i> 电流探头</a>
                                        <a href="../products.html#voltage-probes"><i class="fas fa-bolt"></i> 电压探头</a>
                                        <a href="../products.html#data-acquisition"><i class="fas fa-database"></i> 数据采集卡</a>
                                    </div>
                                    <div class="dropdown-column">
                                        <a href="../products.html#multimeters"><i class="fas fa-tachometer-alt"></i> 万用表/毫伏表</a>
                                        <a href="../products.html#power-supply"><i class="fas fa-plug"></i> 直流供电</a>
                                        <a href="../products.html#signal-generators"><i class="fas fa-signal"></i> 信号发生器</a>
                                        <a href="../products.html#analyzers"><i class="fas fa-chart-line"></i> 射频微波类仪器</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="../services.html">服务支持</a>
                            <div class="dropdown-menu">
                                <a href="../services.html#maintenance"><i class="fas fa-tools"></i> 维修服务</a>
                                <a href="../services.html#calibration"><i class="fas fa-balance-scale"></i> 校准服务</a>
                                <a href="../services.html#custom"><i class="fas fa-cogs"></i> 定制服务</a>
                                <a href="../services.html#training"><i class="fas fa-chalkboard-teacher"></i> 技术培训</a>
                            </div>
                        </li>
                        <li><a href="../about.html">关于我们</a></li>
                        <li><a href="../contact.html">联系我们</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <div class="container">
            <div class="breadcrumb-nav">
                <a href="../index.html"><i class="fas fa-home"></i> 首页</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html">产品中心</a>
                <i class="fas fa-angle-right"></i>
                <a href="../products.html#oscilloscopes-high-resolution">高分辨率示波器</a>
                <i class="fas fa-angle-right"></i>
                <span>MSO3000HD系列高分辨率示波器</span>
            </div>
        </div>
    </div>
    <!-- 产品详情内容 -->
    <main>
        <section class="product-detail">
            <div class="container">
                <div class="product-header">
                    <h1>MSO3000HD系列高分辨率示波器</h1>
                </div>
                <div class="product-overview">
                    <div class="product-image-main">
                        <img src="../优利德/高分辨率示波器/MSO3000HD_1.png" alt="MSO3000HD系列高分辨率示波器" id="main-image">
                        <div class="product-thumbnails">
                            <div class="product-thumbnail" onclick="changeImage('../优利德/高分辨率示波器/MSO3000HD_1.png')">
                                <img src="../优利德/高分辨率示波器/MSO3000HD_1.png" alt="MSO3000HD正面图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/高分辨率示波器/MSO3000HD_2.png')">
                                <img src="../优利德/高分辨率示波器/MSO3000HD_2.png" alt="MSO3000HD功能图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/高分辨率示波器/MSO3000HD_3.png')">
                                <img src="../优利德/高分辨率示波器/MSO3000HD_3.png" alt="MSO3000HD特性图">
                            </div>
                            <div class="product-thumbnail" onclick="changeImage('../优利德/高分辨率示波器/MSO3000HD_4.png')">
                                <img src="../优利德/高分辨率示波器/MSO3000HD_4.png" alt="MSO3000HD应用图">
                            </div>
                        </div>
                    </div>
                    <div class="product-intro">
                        <h2>产品概述</h2>
                        <p>MSO3000HD是一款集成了4信号通道，16通道逻辑分析仪和2通道任意波形发生器的高分辨示波器。配备了R12™12bit分辨率的ADC，可通过ERES增强分辨率至16bit，其最大采样率为2.5GSa/s，存储深度为500Mpts，波形捕获率为1,500,000wfms/s，依此可确保高质量的波形分析。MSO3000HD支持11种触发事件，12种协议数据，还可通过WebServer实现远程操控，适用于医疗、低功耗设备、集成电路、高精度电源、电力、汽车电子等领域的各种测试和分析任务，是一款功能全、性能强大的仪器设备。</p>
                        <h2>产品特点</h2>
                        <ul class="feature-list-columns">
                            <li>模拟通道带宽：500MHz/350MHz/200MHz</li>
                            <li>模拟通道*4 + Gen*2 + LA*16</li>
                            <li>实时采样率高达2.5GSa/s</li>
                            <li>最高存储深度达500Mpts</li>
                            <li>波形捕获率1,500,000wfms/s</li>
                            <li>分辨率12bit(ADC)+ 4bit(ERES)</li>
                            <li>实时频谱、探头自识别、时间趋势图、直方图</li>
                            <li>数学:基本波形代数，FFT，高级公式编辑器</li>
                            <li>内置50MHz双通道函数波形任意发生器（选配）*</li>
                            <li>14种电源分析（选配）*、12种协议解码和分析（选配）*</li>
                            <li>10.1 英寸触控屏</li>
                            <li>丰富的接口：USB 3.0 Host *3、USB 3.0 Device、LAN(千兆)、EXT Trig、AUX Out(Trig Out\Pass/Fail)输出、Gen *2、HDMI、10MHz Ref in/Out、WiFi</li>
                        </ul>
                    </div>
                </div>
                <!-- PDF产品手册 -->
                <div class="pdf-content">
                    <h2>产品手册</h2>
                    <iframe class="pdf-viewer" src="../优利德/高分辨率示波器/MSO3000HD系列 高分辨率示波器.pdf"></iframe>
                </div>
                <!-- 联系信息 -->
                <div class="contact-info">
                    <h3>需要更多产品信息？</h3>
                    <p>如果您对MSO3000HD系列高分辨率示波器有任何疑问或需求，欢迎联系我们的产品专家。</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675 / 020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
            </div>
        </section>
    </main>
    <!-- 页脚同utl8500x.html -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <p><i class="fas fa-map-marker-alt"></i> 地址：广州市天河区黄村三联路20号尚北科创园C栋405</p>
                    <p><i class="fas fa-phone"></i> 电话：18022391675</p>
                    <p><i class="fas fa-phone"></i> 固定电话：020-31801362</p>
                    <p><i class="fas fa-envelope"></i> 邮箱：<EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../products.html">产品中心</a></li>
                        <li><a href="../services.html">服务支持</a></li>
                        <li><a href="../about.html">关于我们</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 广州迅屿科技有限公司 版权所有 | 粤ICP备2025434012号</p>
            </div>
        </div>
    </footer>
    <script>
        function changeImage(src) {
            document.getElementById('main-image').src = src;
        }
    </script>
</body>
</html> 