// 网站产品数据
const productData = [
    {
        title: "泰克高压探头P6015A",
        description: "高压探头 P6015A 是一款 1000X 高压探头，可测量高达 20 kV DC + peak AC（40 kV peak-peak）的电压。适用于高压电源、开关电源等测试场景。",
        url: "p6015a.html",
        category: "电压探头",
        keywords: ["泰克", "高压探头", "P6015A", "电压测量", "示波器配件"]
    },
    {
        title: "刚性罗氏线圈",
        description: "LMR 系列罗氏线圈，适用频带广泛，所采样波形不畸变，具有快速、慢速的上升沿电流波形的时间参数响应能力。",
        url: "lmr-rogowski-coil.html",
        category: "电流探头",
        keywords: ["罗氏线圈", "LMR", "电流测量", "无畸变", "脉知", "HMR", "MMR"]
    },
    {
        title: "MSO3000HD系列高分辨率示波器",
        description: "优利德MSO3000HD系列高分辨率示波器，4通道+16路逻辑+2路任意波形，12bit分辨率，2.5GSa/s采样，500Mpts存储，适用于高质量波形分析。",
        url: "product_details/mso3000hd.html",
        category: "示波器",
        keywords: ["优利德", "高分辨率示波器", "MSO3000HD", "12bit", "2.5GSa/s", "500Mpts", "逻辑分析仪", "任意波形发生器", "医疗", "低功耗", "高精度电源", "汽车电子"]
    },
    {
        title: "MSO5000HD系列高分辨率示波器",
        description: "优利德MSO5000HD系列高分辨率示波器，4通道+16路逻辑+2路任意波形，12bit分辨率，5GSa/s采样，1GHz带宽，500Mpts存储，适用于高速信号分析。",
        url: "product_details/mso5000hd.html",
        category: "示波器",
        keywords: ["优利德", "高分辨率示波器", "MSO5000HD", "12bit", "5GSa/s", "1GHz", "500Mpts", "逻辑分析仪", "任意波形发生器", "通信", "汽车电子", "工业自动化", "科研"]
    },
    {
        title: "MSO8000HD系列高分辨率示波器",
        description: "优利德MSO8000HD系列高分辨率示波器，8GHz/5GHz带宽，20GSa/s采样，12bit分辨率，2Gpts存储，Matlab嵌入，适用于高速及高动态范围信号测试。",
        url: "product_details/mso8000hd.html",
        category: "示波器",
        keywords: ["优利德", "高分辨率示波器", "MSO8000HD", "8GHz", "5GHz", "20GSa/s", "2Gpts", "12bit", "Matlab", "抖动分析", "眼图", "高速信号"]
    },
    {
        title: "MSO7000X系列高带宽混合信号示波器",
        description: "优利德MSO7000X系列高带宽混合信号示波器，2.5GHz带宽，10GSa/s采样，1Gpts存储，丰富协议分析与智能化平台，适用于高速信号分析。",
        url: "product_details/mso7000x.html",
        category: "示波器",
        keywords: ["优利德", "混合信号示波器", "MSO7000X", "2.5GHz", "10GSa/s", "1Gpts", "Win10", "眼图", "抖动分析", "协议分析", "API", "高速信号"]
    },
    {
        title: "UPO7000L系列紧凑型数字荧光示波器",
        description: "优利德UPO7000L系列紧凑型数字荧光示波器，1U高度，10GSa/s采样，1Gpts存储，支持多机同步，适合高密度集成与远程操控。",
        url: "product_details/upo7000l.html",
        category: "示波器",
        keywords: ["优利德", "紧凑型数字荧光示波器", "UPO7000L", "1U", "10GSa/s", "1Gpts", "多机同步", "远程操控", "眼图", "抖动分析", "Matlab", "高密度集成"]
    },
    {
        title: "电子负载 IT8900",
        description: "高性能电子负载，提供精确的电源测试和模拟功能，适用于研发和生产测试环境。",
        url: "products.html#power-loads-electronic",
        category: "功率负载",
        keywords: ["电子负载", "IT8900", "电源测试", "功率测试"]
    },
    {
        title: "大功率直流电子负载",
        description: "高功率电子负载解决方案，支持大电流测试需求，适用于电源和电池测试场景。",
        url: "products.html#power-loads-electronic",
        category: "功率负载",
        keywords: ["电子负载", "大功率", "直流", "电源测试", "电池测试"]
    },
    {
        title: "高速数据采集卡",
        description: "高性能数据采集解决方案，支持高速信号采集和分析，适用于科研和工业测量领域。",
        url: "products.html#data-acquisition-high",
        category: "数据采集卡",
        keywords: ["数据采集", "高速", "信号分析", "科研", "工业测量"]
    },
    {
        title: "多通道数据采集系统",
        description: "支持多通道同步采集的综合数据系统，提供强大的分析和存储能力，适合复杂测试场景。",
        url: "products.html#data-acquisition-multi",
        category: "数据采集卡",
        keywords: ["数据采集", "多通道", "同步采集", "数据存储", "复杂测试"]
    },
    // 射频微波类仪器
    {
        title: "UTS5000A系列信号分析仪",
        description: "优利德UTS5000A系列信号分析仪，提供高精度频谱分析和信号测量功能，适用于射频电路设计和测试。",
        url: "products.html#analyzers-spectrum",
        category: "射频微波类仪器",
        keywords: ["优利德", "信号分析仪", "UTS5000A", "频谱分析", "射频测试"]
    },
    {
        title: "UTS3000A系列信号分析仪",
        description: "优利德UTS3000A系列信号分析仪，性价比高，适合教育和一般工业应用的射频信号分析。",
        url: "products.html#analyzers-spectrum",
        category: "射频微波类仪器",
        keywords: ["优利德", "信号分析仪", "UTS3000A", "频谱分析", "射频测试"]
    },
    {
        title: "UTS3000B系列频谱分析仪",
        description: "优利德UTS3000B系列频谱分析仪，提供稳定可靠的频谱测量功能，适合通信和电子制造行业。",
        url: "products.html#analyzers-spectrum",
        category: "射频微波类仪器",
        keywords: ["优利德", "频谱分析仪", "UTS3000B", "频谱测量", "通信测试"]
    },
    {
        title: "UTS1000B系列频谱分析仪",
        description: "优利德UTS1000B系列频谱分析仪，入门级频谱分析设备，适合基础射频测量和教学应用。",
        url: "products.html#analyzers-spectrum",
        category: "射频微波类仪器",
        keywords: ["优利德", "频谱分析仪", "UTS1000B", "射频测量", "教学应用"]
    },
    {
        title: "USG5000M & USG3000M系列射频模拟信号发生器",
        description: "优利德射频模拟信号发生器，支持9kHz~22GHz频率范围，具有超高频率分辨率0.001Hz和出色的幅度精度，适合通信和射频电路测试。",
        url: "product_details/usg5000m_3000m.html",
        category: "射频微波类仪器",
        keywords: ["优利德", "信号发生器", "USG5000M", "USG3000M", "射频信号", "模拟信号", "高精度"]
    },
    // 函数/任意波形发生器
    {
        title: "UTG4000A系列函数/任意波形发生器",
        description: "优利德UTG4000A系列多功能混合信号发生器，500MSa/s采样速度，16bit垂直分辨率，高达32Mpts任意波存储，适合高精度测试需求。",
        url: "product_details/utg4000a.html",
        category: "信号发生器",
        keywords: ["优利德", "函数发生器", "任意波形", "UTG4000A", "波形输出", "混合信号", "高精度"]
    },
    {
        title: "UTG2000X系列函数/任意波形发生器",
        description: "优利德UTG2000X系列函数/任意波形发生器，经济型高性能多功能信号源，1.25GSa/s采样率，提供10种基本波形和15种调制功能。",
        url: "product_details/utg2000x.html",
        category: "信号发生器",
        keywords: ["优利德", "函数发生器", "任意波形", "UTG2000X", "信号源", "经济型"]
    },
    // 万用表/毫伏表
    {
        title: "UT8806台式数字万用表",
        description: "优利德UT8806 6½位双显示数字万用表，拥有出众的读数速率和精确度，支持多种测量功能及数学运算功能，适合实验室和工业应用。",
        url: "product_details/ut8806.html",
        category: "万用表/毫伏表",
        keywords: ["优利德", "数字万用表", "UT8806", "台式", "6½位", "高精度", "双显示"]
    },
    // 直流供电及老化试验电源负载
    {
        title: "UDP4303S可编程线性直流电源",
        description: "优利德UDP4303S可编程线性直流电源，四通道独立设计，CH1/CH2:32V/3A，CH3:15V/3A，CH4:6V/10A，支持一键等性能串并联，适用于半导体、传感器、IoT测试领域。",
        url: "product_details/udp4303s.html",
        category: "直流供电",
        keywords: ["优利德", "直流电源", "UDP4303S", "可编程", "线性电源", "四通道", "低纹波噪声"]
    },
    {
        title: "UTL8500X+系列可编程直流电子负载",
        description: "优利德UTL8500X+系列直流电子负载，4.3英寸TFT液晶大屏，高达500kHz的高速同步采样，测量范围500V/120A，600W，支持CC、CV、CR、CP四种基本模式。",
        url: "product_details/utl8500x.html",
        category: "功率负载",
        keywords: ["优利德", "电子负载", "UTL8500X+", "可编程", "直流负载", "波形显示", "高速采样"]
    },
    {
        title: "UTL8200+系列可编程直流电子负载",
        description: "优利德UTL8200+系列电子负载，支持双通道完全独立测试，最高可达1mV/1mA高分辨率，支持多模式电池放电测试和列表模式自动测试。",
        url: "product_details/utl8200.html",
        category: "功率负载",
        keywords: ["优利德", "电子负载", "UTL8200+", "可编程", "直流负载", "双通道", "电池放电测试"]
    }
];

// 服务数据
const serviceData = [
    {
        title: "维修服务",
        description: "提供专业的仪器维修和校准服务",
        url: "services.html#maintenance",
        category: "服务支持",
        keywords: ["维修", "校准", "仪器维护"]
    },
    {
        title: "校准服务",
        description: "提供精准的仪器校准服务",
        url: "services.html#calibration",
        category: "服务支持",
        keywords: ["校准", "精度", "仪器校准"]
    },
    {
        title: "定制服务",
        description: "根据客户需求提供定制化解决方案",
        url: "services.html#custom",
        category: "服务支持",
        keywords: ["定制", "解决方案", "个性化"]
    },
    {
        title: "技术培训",
        description: "提供专业的技术培训服务",
        url: "services.html#training",
        category: "服务支持",
        keywords: ["培训", "技术支持", "教育"]
    }
];

// 合并所有数据
const allData = [...productData, ...serviceData];

// 获取URL参数
function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

// 搜索函数
function searchItems(query) {
    if (!query) return [];
    
    query = query.toLowerCase();
    return allData.filter(item => {
        // 搜索标题
        if (item.title.toLowerCase().includes(query)) return true;
        
        // 搜索描述
        if (item.description.toLowerCase().includes(query)) return true;
        
        // 搜索分类
        if (item.category.toLowerCase().includes(query)) return true;
        
        // 搜索关键词
        if (item.keywords && item.keywords.some(keyword => keyword.toLowerCase().includes(query))) return true;
        
        return false;
    });
}

// 渲染搜索结果
function renderSearchResults(results) {
    const searchResultsElement = document.getElementById('search-results');
    const noResultsElement = document.getElementById('no-results');
    
    // 清空现有结果
    searchResultsElement.innerHTML = '';
    
    if (results.length === 0) {
        searchResultsElement.style.display = 'none';
        noResultsElement.style.display = 'block';
        return;
    }
    
    // 显示结果
    searchResultsElement.style.display = 'block';
    noResultsElement.style.display = 'none';
    
    results.forEach(result => {
        const resultElement = document.createElement('div');
        resultElement.className = 'search-result-item';
        
        resultElement.innerHTML = `
            <h3><a href="${result.url}">${result.title}</a></h3>
            <p class="result-category">${result.category}</p>
            <p class="result-description">${result.description}</p>
        `;
        
        searchResultsElement.appendChild(resultElement);
    });
}

// 页面加载时处理搜索
document.addEventListener('DOMContentLoaded', () => {
    // 获取搜索关键词
    const query = getQueryParam('q');
    
    if (query) {
        // 显示搜索关键词
        document.getElementById('search-keyword').textContent = query;
        document.getElementById('search-input').value = query;
        
        // 执行搜索
        const results = searchItems(query);
        renderSearchResults(results);
    }
    
    // 添加搜索框事件监听
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    
    // 点击搜索按钮
    searchButton.addEventListener('click', () => {
        const query = searchInput.value.trim();
        if (query) {
            window.location.href = `search.html?q=${encodeURIComponent(query)}`;
        }
    });
    
    // 按回车键搜索
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `search.html?q=${encodeURIComponent(query)}`;
            }
        }
    });
}); 